../../Scripts/argos-translate,sha256=3vWYaF9tXlql9BuSGNWu9LCt01xIncIAPMq4q-uROCw,98
../../Scripts/argospm,sha256=jheNoYjkxcvVD1XSVlr4FwvujesBuEIV5GHZoHIt7xA,103
argostranslate-1.9.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
argostranslate-1.9.6.dist-info/LICENSE,sha256=-Qhmq3TBt1bMb3QgrtEJ9ZgyDJtmCurbzsZFjQwaTh8,1085
argostranslate-1.9.6.dist-info/METADATA,sha256=btCE6fpV1DmbF6c3IKjyNpcQkVtl0n7I8oIasnFV7uM,10061
argostranslate-1.9.6.dist-info/RECORD,,
argostranslate-1.9.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
argostranslate-1.9.6.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
argostranslate-1.9.6.dist-info/top_level.txt,sha256=kzsngYnezFUuZ4e5uf0126V96MENdNlZkNS_0DO09YQ,21
argostranslate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
argostranslate/__pycache__/__init__.cpython-310.pyc,,
argostranslate/__pycache__/apis.cpython-310.pyc,,
argostranslate/__pycache__/apply_bpe.cpython-310.pyc,,
argostranslate/__pycache__/argospm.cpython-310.pyc,,
argostranslate/__pycache__/cli.cpython-310.pyc,,
argostranslate/__pycache__/fewshot.cpython-310.pyc,,
argostranslate/__pycache__/models.cpython-310.pyc,,
argostranslate/__pycache__/networking.cpython-310.pyc,,
argostranslate/__pycache__/package.cpython-310.pyc,,
argostranslate/__pycache__/sbd.cpython-310.pyc,,
argostranslate/__pycache__/settings.cpython-310.pyc,,
argostranslate/__pycache__/tags.cpython-310.pyc,,
argostranslate/__pycache__/tokenizer.cpython-310.pyc,,
argostranslate/__pycache__/translate.cpython-310.pyc,,
argostranslate/__pycache__/utils.cpython-310.pyc,,
argostranslate/apis.py,sha256=c-sxSZeFN6W8_NfHMfwTTzUM6rEA3buDKzg0Tyb3mWM,4324
argostranslate/apply_bpe.py,sha256=RBI-4GJh9tW17PCncpYUeLisWLHWXhHZD0DRknJUZ5A,14371
argostranslate/argospm.py,sha256=bF9z_2feJ6eUszWVKzQhNKl1R_m8-SSQ-LGD1ssAAG8,4313
argostranslate/cli.py,sha256=VGhZ2X7yhgSxhCK6svYjYL_TtZ8jtqYWJd2KfysqzwU,2147
argostranslate/fewshot.py,sha256=dxtUiQ0lH_KXtaJRAkMacsk95IwYTAEJw6F0_OeJCpg,2240
argostranslate/models.py,sha256=SROfUPvndMrRlaDK4LM6imYy0_7xyyoQQB2jrB2efgc,298
argostranslate/networking.py,sha256=fISGEHG20A3E7yDBV_u0Kz8kpRfc33pQg1jb0o_uk-U,2269
argostranslate/package.py,sha256=3nnqjmfVXxfCYeDz7lGtYlhh0p-o3R6xXzVvkQfa9m0,13220
argostranslate/sbd.py,sha256=_UOwd96fN5bHcvC_wSgp2_06_MeTi0JRF-DRO_-aL3Y,3287
argostranslate/settings.py,sha256=JR-d6Jj8x_b5LSBpt-Mpuog8kkHWIPfm-6c0lKCqUaU,3119
argostranslate/tags.py,sha256=r9_35VFECaqtRuv2DttckxH3shzQYQnKYOBMl2SHVuI,6544
argostranslate/tokenizer.py,sha256=T6PALeVIH3j2H2KcfLbFOqx3If5oq3-3MaXRvzTJBQ8,2268
argostranslate/translate.py,sha256=VEB_xGIlPpE1UtCuxd3UI-PQdNM2-o2ln8iOgXWPWcQ,23802
argostranslate/utils.py,sha256=WSIHLUTVnmlsfjxKArL6r19iP-tTEaKgm4xmCeneotA,487
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-310.pyc,,
tests/__pycache__/test_package.cpython-310.pyc,,
tests/__pycache__/test_translate.cpython-310.pyc,,
tests/test_package.py,sha256=3Vgt13PeBsQX3M_wI8xrhHB2JWxYCAkjxhogobJyrUk,2489
tests/test_translate.py,sha256=M2dQuryfis7rZHqVxTF3rmlMcxkLgaCSMcMsbjYhdr4,4581
