# Core dependencies (existing)
faster-whisper
torch
edge-tts
pygame
pyaudio
numpy
googletrans==4.0.0rc1
customtkinter
requests
pydub
g4f
RealtimeSTT

# New realtime translation dependencies
argostranslate
dl-translate
RealtimeTTS[all]

# Optional: For better neural TTS quality
# coqui-tts  # Uncomment if you want high-quality neural TTS (requires 4-5GB VRAM)

# Installation notes:
# 1. Install basic realtime support:
#    pip install argostranslate dl-translate RealtimeTTS
#
# 2. For full neural TTS support (requires GPU):
#    pip install RealtimeTTS[all] coqui-tts
#
# 3. For Argos language packages, run after installation:
#    python -c "import argostranslate.package; argostranslate.package.update_package_index()"
#    python -c "import argostranslate.package; available_packages = argostranslate.package.get_available_packages(); packages_to_install = [pkg for pkg in available_packages if pkg.from_code == 'en']; [pkg.install() for pkg in packages_to_install[:10]]"
