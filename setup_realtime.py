#!/usr/bin/env python3
"""
Setup script for realtime translation dependencies
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def install_basic_dependencies():
    """Install basic realtime translation dependencies"""
    print("📦 Installing basic realtime translation dependencies...")
    
    commands = [
        ("pip install argostranslate", "Installing Argos Translate"),
        ("pip install dl-translate", "Installing dl-translate"),
        ("pip install RealtimeTTS", "Installing RealtimeTTS"),
    ]
    
    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1
    
    return success_count == len(commands)

def install_neural_tts():
    """Install neural TTS dependencies (optional)"""
    print("\n🧠 Installing neural TTS dependencies (optional)...")
    print("⚠️  This requires significant disk space and GPU memory for optimal performance")
    
    response = input("Do you want to install neural TTS (Coqui TTS)? [y/N]: ").lower()
    if response in ['y', 'yes']:
        return run_command("pip install RealtimeTTS[all] coqui-tts", "Installing neural TTS")
    else:
        print("⏭️  Skipping neural TTS installation")
        return True

def setup_argos_packages():
    """Setup Argos Translate language packages"""
    print("\n🌍 Setting up Argos Translate language packages...")
    
    try:
        import argostranslate.package
        
        # Update package index
        print("🔄 Updating Argos package index...")
        argostranslate.package.update_package_index()
        print("✅ Package index updated")
        
        # Get available packages
        available_packages = argostranslate.package.get_available_packages()
        
        # Install English to other language packages (most common use case)
        en_packages = [pkg for pkg in available_packages if pkg.from_code == 'en']
        
        print(f"📋 Found {len(en_packages)} English translation packages")
        print("🔄 Installing top 15 language packages...")
        
        # Install top 15 most common languages
        popular_languages = ['es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi', 'nl', 'sv', 'no', 'da']
        installed_count = 0
        
        for pkg in en_packages:
            if pkg.to_code in popular_languages and installed_count < 15:
                try:
                    print(f"  📥 Installing English → {pkg.to_code} ({pkg.to_name})")
                    pkg.install()
                    installed_count += 1
                except Exception as e:
                    print(f"  ⚠️  Failed to install {pkg.to_name}: {e}")
        
        print(f"✅ Installed {installed_count} language packages")
        return True
        
    except ImportError:
        print("❌ Argos Translate not available. Install it first with: pip install argostranslate")
        return False
    except Exception as e:
        print(f"❌ Error setting up Argos packages: {e}")
        return False

def test_installations():
    """Test if the installations work"""
    print("\n🧪 Testing installations...")
    
    tests = [
        ("import argostranslate.translate", "Argos Translate"),
        ("import dl_translate", "dl-translate"),
        ("from RealtimeTTS import TextToAudioStream, SystemEngine", "RealtimeTTS"),
    ]
    
    success_count = 0
    for test_code, name in tests:
        try:
            exec(test_code)
            print(f"✅ {name} working correctly")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} not working: {e}")
        except Exception as e:
            print(f"⚠️  {name} test failed: {e}")
    
    return success_count == len(tests)

def main():
    """Main setup function"""
    print("🚀 Realtime Translation Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install basic dependencies
    if not install_basic_dependencies():
        print("\n❌ Failed to install basic dependencies. Please check the errors above.")
        sys.exit(1)
    
    # Install neural TTS (optional)
    install_neural_tts()
    
    # Setup Argos packages
    setup_argos_packages()
    
    # Test installations
    if test_installations():
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Run your translator: python TRANSLATE.py")
        print("2. Select 'Realtime (Local)' from the translator dropdown")
        print("3. Choose your preferred realtime engine")
        print("4. Enjoy local, offline realtime translation!")
    else:
        print("\n⚠️  Setup completed with some issues. Check the test results above.")
    
    print("\n💡 Tips:")
    print("- Use 'Argos (Local)' for completely offline translation")
    print("- Use 'dl-translate (Local)' for high-quality neural translation")
    print("- Use 'Google (Fast)' for fastest translation (requires internet)")

if __name__ == "__main__":
    main()
