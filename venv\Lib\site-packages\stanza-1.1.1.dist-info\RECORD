stanza-1.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
stanza-1.1.1.dist-info/LICENSE,sha256=nmWttJaY5vgSI9gbrg7gCsTd88uNQpuF74Xji5XqilA,603
stanza-1.1.1.dist-info/METADATA,sha256=SDQlLaL_4m0w-hE9x6UjxVRWel6ywf_xJGguclmQqHU,10680
stanza-1.1.1.dist-info/RECORD,,
stanza-1.1.1.dist-info/WHEEL,sha256=g4nMs7d-Xl9-xC9XovUrsDHGXt-FT0E17Yqo92DEfvY,92
stanza-1.1.1.dist-info/top_level.txt,sha256=uiWo1sAzjzP-ZyLp8H6s3vPgLekAYPumeRUDtnfQIvc,13
stanza/__init__.py,sha256=Oz7Ln4duFnj1XUh4arbDrhc542lqLQI_7yEyH-HYM_c,943
stanza/__pycache__/__init__.cpython-310.pyc,,
stanza/__pycache__/_version.cpython-310.pyc,,
stanza/_version.py,sha256=gfx3tQmdcTAFwKMsHkvPciiyFzyeu66wLUc4Mc9jLrU,105
stanza/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/__pycache__/__init__.cpython-310.pyc,,
stanza/models/__pycache__/_training_logging.cpython-310.pyc,,
stanza/models/__pycache__/charlm.cpython-310.pyc,,
stanza/models/__pycache__/classifier.cpython-310.pyc,,
stanza/models/__pycache__/identity_lemmatizer.cpython-310.pyc,,
stanza/models/__pycache__/lemmatizer.cpython-310.pyc,,
stanza/models/__pycache__/mwt_expander.cpython-310.pyc,,
stanza/models/__pycache__/ner_tagger.cpython-310.pyc,,
stanza/models/__pycache__/parser.cpython-310.pyc,,
stanza/models/__pycache__/tagger.cpython-310.pyc,,
stanza/models/__pycache__/tokenizer.cpython-310.pyc,,
stanza/models/_training_logging.py,sha256=1xMYIF_qAGZ0kkw-OQrL2sGMGzL9zO7AHCmDPxtbnjk,83
stanza/models/charlm.py,sha256=kj4WOmu56MY8KJ5cfT4PBKt9wXXoXpSBazmyWmVTrdA,15095
stanza/models/classifier.py,sha256=t0aOkwfj-jxw7LLQRKXq3Hk3RR5N854qiMjJz2rWadE,26234
stanza/models/classifiers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/classifiers/__pycache__/__init__.cpython-310.pyc,,
stanza/models/classifiers/__pycache__/classifier_args.cpython-310.pyc,,
stanza/models/classifiers/__pycache__/cnn_classifier.cpython-310.pyc,,
stanza/models/classifiers/__pycache__/iterate_test.cpython-310.pyc,,
stanza/models/classifiers/classifier_args.py,sha256=gAo6enrTkm4hmpBOKJBqeTFEw9vVAJV5qSOcr3lTtRY,2224
stanza/models/classifiers/cnn_classifier.py,sha256=5VWoCWZo9Ym_FP9xGnCMq-WSQjqFzNMPXm0tEi24j0Q,15361
stanza/models/classifiers/iterate_test.py,sha256=I3lt0NybVqimkUHaiOu19DHLi_SUhiP-gX4AK-me8qw,2432
stanza/models/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/common/__pycache__/__init__.cpython-310.pyc,,
stanza/models/common/__pycache__/beam.cpython-310.pyc,,
stanza/models/common/__pycache__/biaffine.cpython-310.pyc,,
stanza/models/common/__pycache__/char_model.cpython-310.pyc,,
stanza/models/common/__pycache__/chuliu_edmonds.cpython-310.pyc,,
stanza/models/common/__pycache__/constant.cpython-310.pyc,,
stanza/models/common/__pycache__/crf.cpython-310.pyc,,
stanza/models/common/__pycache__/data.cpython-310.pyc,,
stanza/models/common/__pycache__/doc.cpython-310.pyc,,
stanza/models/common/__pycache__/dropout.cpython-310.pyc,,
stanza/models/common/__pycache__/hlstm.cpython-310.pyc,,
stanza/models/common/__pycache__/loss.cpython-310.pyc,,
stanza/models/common/__pycache__/packed_lstm.cpython-310.pyc,,
stanza/models/common/__pycache__/pretrain.cpython-310.pyc,,
stanza/models/common/__pycache__/seq2seq_constant.cpython-310.pyc,,
stanza/models/common/__pycache__/seq2seq_model.cpython-310.pyc,,
stanza/models/common/__pycache__/seq2seq_modules.cpython-310.pyc,,
stanza/models/common/__pycache__/seq2seq_utils.cpython-310.pyc,,
stanza/models/common/__pycache__/trainer.cpython-310.pyc,,
stanza/models/common/__pycache__/utils.cpython-310.pyc,,
stanza/models/common/__pycache__/vocab.cpython-310.pyc,,
stanza/models/common/beam.py,sha256=AhId7YSk-KNKJrBod9KbLtxWW7VEWI2AzsArk8dv45s,3907
stanza/models/common/biaffine.py,sha256=jJ3NGgma-Oi0DjDHdWVqHXshCA0-XDvl_8F2V_8TZzM,3582
stanza/models/common/char_model.py,sha256=UqNpMSuNqEqXq_uYLzkzmWrsTlIQGYXUMrGVHgBTuFg,6723
stanza/models/common/chuliu_edmonds.py,sha256=qOsvg47wigH8wTtPRdWyvFDrldghnwy8wU2-M5EEaAk,6708
stanza/models/common/constant.py,sha256=pBvzAIXBS4M066w0-cYTcryrozJODVJ8QGrI9kQY61M,2012
stanza/models/common/crf.py,sha256=ni3V8b6_vbZLOefjeTnpntx8o0q7zue5m_mPBAMCLvA,5418
stanza/models/common/data.py,sha256=Y9QZXkOh0x7Hgd9zak-y6Y6RjNE95_CII-LQnAON3Io,1412
stanza/models/common/doc.py,sha256=g144vXhHkTh4fj0mptBu4HRzRE6mLRPBn6WS0Xi0fmQ,34223
stanza/models/common/dropout.py,sha256=xGY8vrtkhTNYiLc-no5YJ1S1TuwmEsNSow6KLWgfj78,2856
stanza/models/common/hlstm.py,sha256=DA2iRSKX9O_C09B5-sFkkBA8uO_JdAR3MfH5oUj95Bg,5088
stanza/models/common/loss.py,sha256=3yQGCe2w69RuNrk-4WpSdNLk0NKTXgZw9omzq6iujoE,2660
stanza/models/common/packed_lstm.py,sha256=AcU3g1uoQZASk-jFCd9qeIIlm9dPu1FJ4AbQnZ8vKzw,4855
stanza/models/common/pretrain.py,sha256=ilBJ0oqd7wehqaGqbudEQpxgPy79okKZWuUiHOUYKRE,5133
stanza/models/common/seq2seq_constant.py,sha256=2bj6cG8ig2Jw1zOT9F1xBP0-kg7bTNfHARSUKuLVbok,221
stanza/models/common/seq2seq_model.py,sha256=E8yVJICEG5-XyH_Bm4WX_GavcokDub72X9bacJGVQU8,11832
stanza/models/common/seq2seq_modules.py,sha256=Lykora2ZRSynkhwG1NnE58gO0B9M3FYFCKxw88bCgg0,8014
stanza/models/common/seq2seq_utils.py,sha256=7vvwfvpSJuLnz90vilqqhlu4zx42yj3WWMVuzq05mTw,3506
stanza/models/common/trainer.py,sha256=KJvMDQeZpXvn7Tl91SCl_OmnZUPti4W0FifrQvuDTs0,645
stanza/models/common/utils.py,sha256=i1jkrSP1gpds3cCpBwJWgtbf8njJhEc2bUfdcDiJ_YE,7284
stanza/models/common/vocab.py,sha256=u5lj--uNxQtL8DAzvIxJiBG0krIqSKM1GTZdD29Y2xY,7833
stanza/models/depparse/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/depparse/__pycache__/__init__.cpython-310.pyc,,
stanza/models/depparse/__pycache__/data.cpython-310.pyc,,
stanza/models/depparse/__pycache__/model.cpython-310.pyc,,
stanza/models/depparse/__pycache__/scorer.cpython-310.pyc,,
stanza/models/depparse/__pycache__/trainer.cpython-310.pyc,,
stanza/models/depparse/data.py,sha256=IuJUgUtSPCOIKiqUjwdTtKhhPNg4jSzZ_bcas4BpyXI,9183
stanza/models/depparse/model.py,sha256=56ohe0uTC5P0KZp2UQODVmeW512cKytpvohuDu4NcUI,10183
stanza/models/depparse/scorer.py,sha256=A6otqrNURcmXyBZih1W0WrQ5IXV8O28ivkMcaMPcbmE,614
stanza/models/depparse/trainer.py,sha256=gNpsbJyAhikNBLpU9MF2b_Wcn5mO8NHygb-9R-R87Q4,5227
stanza/models/identity_lemmatizer.py,sha256=jAz0CFp6OLDKecs1FE1e9h7GBruvIdxz9ueMUZ9tko4,2188
stanza/models/lemma/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/lemma/__pycache__/__init__.cpython-310.pyc,,
stanza/models/lemma/__pycache__/data.cpython-310.pyc,,
stanza/models/lemma/__pycache__/edit.cpython-310.pyc,,
stanza/models/lemma/__pycache__/scorer.cpython-310.pyc,,
stanza/models/lemma/__pycache__/trainer.cpython-310.pyc,,
stanza/models/lemma/__pycache__/vocab.cpython-310.pyc,,
stanza/models/lemma/data.py,sha256=mYjCzRo60mVPEgR2fl0Wgx3YsGonA9zKz43nuwq2YhY,4539
stanza/models/lemma/edit.py,sha256=m1he_5Ry8OIZTN7iCerwbNLXdijTRzfgTeM4hD6KP4o,631
stanza/models/lemma/scorer.py,sha256=ost8nwt5AnGN9s40LyAYGxdgs7j2zjGTIlZKRsbOhdI,459
stanza/models/lemma/trainer.py,sha256=L5uooxzxItG7kCon-VSFgLQDpp4PwR7F1nvyq6LZ2lE,8096
stanza/models/lemma/vocab.py,sha256=goKLUJXU8KwsWWW-EO6lA27CDjHDXR91GSqhOARoxW8,649
stanza/models/lemmatizer.py,sha256=HQMnBgf4KC3PXrgCKFwixRb0jte8lhJh5-j0TVTnaV8,11182
stanza/models/mwt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/mwt/__pycache__/__init__.cpython-310.pyc,,
stanza/models/mwt/__pycache__/data.cpython-310.pyc,,
stanza/models/mwt/__pycache__/scorer.cpython-310.pyc,,
stanza/models/mwt/__pycache__/trainer.cpython-310.pyc,,
stanza/models/mwt/__pycache__/vocab.cpython-310.pyc,,
stanza/models/mwt/data.py,sha256=wghWnojQO-Ka9hl279uqggmkxNHxnZHzg3FlUsHiXXA,3545
stanza/models/mwt/scorer.py,sha256=Lj87LhYLuwCvHn7IpX4VHabbiAYp1FEnT0s9MWKPMNY,357
stanza/models/mwt/trainer.py,sha256=BGpacDtbTyYXUxIgJCuY3_7q3VtMJ7xVcq1SHeU0QQk,5617
stanza/models/mwt/vocab.py,sha256=arJ79RRSSf4s00oMDSyk74sUvnfKIh-O9Dx3vtEx1gs,506
stanza/models/mwt_expander.py,sha256=ZvYDxSSGDuNVKslMnWCWlTzO3omPjsxrTB18L13Lorw,11273
stanza/models/ner/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/ner/__pycache__/__init__.cpython-310.pyc,,
stanza/models/ner/__pycache__/data.cpython-310.pyc,,
stanza/models/ner/__pycache__/model.cpython-310.pyc,,
stanza/models/ner/__pycache__/scorer.cpython-310.pyc,,
stanza/models/ner/__pycache__/trainer.cpython-310.pyc,,
stanza/models/ner/__pycache__/utils.cpython-310.pyc,,
stanza/models/ner/__pycache__/vocab.cpython-310.pyc,,
stanza/models/ner/data.py,sha256=EzLlwZQaEQOgdudI4rhZ_-lwAqX9mxO5ukwBAtSuDIk,8839
stanza/models/ner/model.py,sha256=HMJLIJ_sUDEGRC_qkKqBxgzDczJQkOTbs3QNQMduWPM,6294
stanza/models/ner/scorer.py,sha256=_g1nk2cVU4006IjWrZx1EFWWrYt4VB5BYYpEy-Sxg30,4489
stanza/models/ner/trainer.py,sha256=prFi2ZUoPZVd-jijz7HTh5KVD4XX6ragApwpOmDjJLg,4869
stanza/models/ner/utils.py,sha256=DwFd_HWMMWhgN4O0bjpYFkxf9zlMgpkylryGjL5gWMA,3557
stanza/models/ner/vocab.py,sha256=pe3jPYznIdopULgOy9t_ZrVYGkqFEEHblnwJMFIwB70,1553
stanza/models/ner_tagger.py,sha256=qHh0rPr5ytcWRzJuadM4bLGXZbNkaM5hM4K_KlpkCwc,11646
stanza/models/parser.py,sha256=xBN69gHvx5VnQlknwxgAtF1C_2ubad7-xysvD6FcClk,11431
stanza/models/pos/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/pos/__pycache__/__init__.cpython-310.pyc,,
stanza/models/pos/__pycache__/build_xpos_vocab_factory.cpython-310.pyc,,
stanza/models/pos/__pycache__/data.cpython-310.pyc,,
stanza/models/pos/__pycache__/model.cpython-310.pyc,,
stanza/models/pos/__pycache__/scorer.cpython-310.pyc,,
stanza/models/pos/__pycache__/trainer.cpython-310.pyc,,
stanza/models/pos/__pycache__/vocab.cpython-310.pyc,,
stanza/models/pos/__pycache__/xpos_vocab_factory.cpython-310.pyc,,
stanza/models/pos/build_xpos_vocab_factory.py,sha256=4LoJEtT6QktmKy2pDwYtnttifPd7EJPyN1yhJYu8R8s,3996
stanza/models/pos/data.py,sha256=cHxSvs4by8aopJ4hYXg60Rm5KiEZqMTMKfX2D4bNA2Y,6471
stanza/models/pos/model.py,sha256=JQER0an6Uudmug-cKgpQSeUKHPpYD8qHQs_wadlF6p4,8141
stanza/models/pos/scorer.py,sha256=I0Y9HBM5qtuHq9JDEK9vIZkb-OiXA2KRenhYQckezMo,649
stanza/models/pos/trainer.py,sha256=3G9NkNUfShDRepiaHmav4rg_jViA7qG0Cem4c_Hk4KA,5168
stanza/models/pos/vocab.py,sha256=CnQVPnLQhpimMnVHOQgxpS6mfy6L42ANW2o4RHra-BQ,3273
stanza/models/pos/xpos_vocab_factory.py,sha256=xuLDay-dlxvxEyHkxdvmEZcNdoP9uK9anSSqyE1gGzg,2109
stanza/models/tagger.py,sha256=ODu4XRLI9TcVatX7oaYcjLsTXCYNwGETaj189xw01lM,11845
stanza/models/tokenize/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/models/tokenize/__pycache__/__init__.cpython-310.pyc,,
stanza/models/tokenize/__pycache__/data.cpython-310.pyc,,
stanza/models/tokenize/__pycache__/model.cpython-310.pyc,,
stanza/models/tokenize/__pycache__/trainer.cpython-310.pyc,,
stanza/models/tokenize/__pycache__/utils.cpython-310.pyc,,
stanza/models/tokenize/__pycache__/vocab.cpython-310.pyc,,
stanza/models/tokenize/data.py,sha256=Fxdj2cRV9JFQfxq0CIiTOd8NPdoQiJc2iRiTZYVm8U0,8365
stanza/models/tokenize/model.py,sha256=IbcCOQqgYaf970yvYOqQNsxQf4Nt3by9oshPCbC84E8,3427
stanza/models/tokenize/trainer.py,sha256=SsdNwVuQn1Ln2yE2zv9gJ4A8kgx2bcVCowKFbPZlnOY,3249
stanza/models/tokenize/utils.py,sha256=iniRRHuBz5TqF7FLyh4SIJa-scOUIdVSsfjzUkr7qD4,8104
stanza/models/tokenize/vocab.py,sha256=bFAr71YOn8K36FN4uu8oQA7OYHD_TyncutvyyKuZxxU,1305
stanza/models/tokenizer.py,sha256=fX1JAziGiGSpCyCd_NVOohR3srEdqZutHsFpooFTFog,9434
stanza/pipeline/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/pipeline/__pycache__/__init__.cpython-310.pyc,,
stanza/pipeline/__pycache__/_constants.cpython-310.pyc,,
stanza/pipeline/__pycache__/core.cpython-310.pyc,,
stanza/pipeline/__pycache__/depparse_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/lemma_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/mwt_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/ner_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/pos_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/registry.cpython-310.pyc,,
stanza/pipeline/__pycache__/sentiment_processor.cpython-310.pyc,,
stanza/pipeline/__pycache__/tokenize_processor.cpython-310.pyc,,
stanza/pipeline/_constants.py,sha256=bPfCS53ZFUxzhJoqhICHTQzblTzqs6TNGlTCwrR1Ky8,194
stanza/pipeline/core.py,sha256=453t6-OFWotdh0FSvqHRjM9O8GBAYub0YkbBjfEfTBI,7830
stanza/pipeline/depparse_processor.py,sha256=RRGGfa5GaCNc1rtMzvYZrQKB5BBwdIc6QXJX0sCSMDE,2045
stanza/pipeline/external/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/pipeline/external/__pycache__/__init__.cpython-310.pyc,,
stanza/pipeline/external/__pycache__/jieba.cpython-310.pyc,,
stanza/pipeline/external/__pycache__/spacy.cpython-310.pyc,,
stanza/pipeline/external/__pycache__/sudachipy.cpython-310.pyc,,
stanza/pipeline/external/jieba.py,sha256=3Pi3As5kdlmu0O5Qn_zZCbFXtRfNuY7OqfqwOJ_3dOw,2147
stanza/pipeline/external/spacy.py,sha256=-Qctz9jvNNU6wDzMrrTgTpz802z7g7DBnVMS1BYYP6c,2277
stanza/pipeline/external/sudachipy.py,sha256=CfoSZjeqvd3lsfDrEAm_U9lh7y1LfFS7zM1K-ZuMr4U,2690
stanza/pipeline/lemma_processor.py,sha256=E8DEYbYj8lYH14GAyBE7lQZIsZR7I1ed50mC4RIhrww,3734
stanza/pipeline/mwt_processor.py,sha256=-3c11z4s7v1TqUgP3iPUnUcShIQMccuZjXlPVq3s6iU,1514
stanza/pipeline/ner_processor.py,sha256=7xiRmlY_G4oxGCNCrLNuvdADKeHIk8prIbRqQhSCwFI,1552
stanza/pipeline/pos_processor.py,sha256=qXDYMNOxphvf7xQynV6R1MSM9d8B2oB6VeHOqJTaQ4o,1435
stanza/pipeline/processor.py,sha256=OnqrBv5tzjvXV1no-lpf3HkD9MFLfaqhQU-jBXjxLy0,8295
stanza/pipeline/registry.py,sha256=7-rl6zQmlJH-YpN_PoHxtTeJ8yDnq56UJRx7rpNwVRE,129
stanza/pipeline/sentiment_processor.py,sha256=A9NWQ2LteAv2QDd7DVCxhtahc2hcCFQD6zzo0cH0qZ4,1758
stanza/pipeline/tokenize_processor.py,sha256=aCDsKh4ZKlbuKbtXjnZMGoQCQhrIJ6XSOOiMsAJ1G34,3962
stanza/protobuf/CoreNLP_pb2.py,sha256=auOO3He2l3v-pl0HznuSJKY4VqHhS4fMd44cnizhUrs,182057
stanza/protobuf/__init__.py,sha256=vVZd7KaViv7HFuI3NltIgM5Lagu6wYA_nZpxoZ51Vk8,1701
stanza/protobuf/__pycache__/CoreNLP_pb2.cpython-310.pyc,,
stanza/protobuf/__pycache__/__init__.cpython-310.pyc,,
stanza/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/resources/__pycache__/__init__.cpython-310.pyc,,
stanza/resources/__pycache__/common.cpython-310.pyc,,
stanza/resources/__pycache__/installation.cpython-310.pyc,,
stanza/resources/__pycache__/prepare_resources.cpython-310.pyc,,
stanza/resources/common.py,sha256=DjWi612iEbFaTKONhpct83KIufx7qHP_yn4uua3bryg,15038
stanza/resources/installation.py,sha256=A5_TsClOHJtFAi20aJFbce43uxbaEuQquXtehu3y-hE,4382
stanza/resources/prepare_resources.py,sha256=Vp2co-mHFQO8esSp59sFRpiG41WTaiYWI24jaNB1uyI,9320
stanza/server/__init__.py,sha256=WW5IrvAsk_XlukODeI7Dp79OH6XkSqBIa1FVV-jCOm8,624
stanza/server/__pycache__/__init__.cpython-310.pyc,,
stanza/server/__pycache__/annotator.cpython-310.pyc,,
stanza/server/__pycache__/client.cpython-310.pyc,,
stanza/server/__pycache__/main.cpython-310.pyc,,
stanza/server/__pycache__/semgrex.cpython-310.pyc,,
stanza/server/annotator.py,sha256=RpfH_LMw8QRWdx8alFKYF0AtGwsKgXlIYPSQ1Qeoy6c,4671
stanza/server/client.py,sha256=sFYapTbjUV1sndhxNDNWLnLmiHHW3G4a1NpXrhEKqoY,27859
stanza/server/main.py,sha256=I-e38kjvRs6Tm5vxWQi_q7-d5TfeRJXORuPcGUj9PO0,2619
stanza/server/semgrex.py,sha256=YJ8he6RSNtZm5zp2ZxiOwNsoJSjARZQZv3PMYftMEzI,2584
stanza/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stanza/utils/__pycache__/__init__.cpython-310.pyc,,
stanza/utils/__pycache__/avg_sent_len.cpython-310.pyc,,
stanza/utils/__pycache__/conll.cpython-310.pyc,,
stanza/utils/__pycache__/conll18_ud_eval.cpython-310.pyc,,
stanza/utils/__pycache__/contract_mwt.cpython-310.pyc,,
stanza/utils/__pycache__/helper_func.cpython-310.pyc,,
stanza/utils/__pycache__/max_mwt_length.cpython-310.pyc,,
stanza/utils/__pycache__/postprocess_vietnamese_tokenizer_data.cpython-310.pyc,,
stanza/utils/__pycache__/prepare_ner_data.cpython-310.pyc,,
stanza/utils/__pycache__/prepare_tokenizer_data.cpython-310.pyc,,
stanza/utils/__pycache__/select_backoff.cpython-310.pyc,,
stanza/utils/avg_sent_len.py,sha256=sWPAP9lWmoTrMSipbX5vnLTwfLi0DkoptCwhBWkrBcc,402
stanza/utils/conll.py,sha256=PCkX1Q--B21BQnXwgINQQILePurqdkz6oiIfcwBGLHY,6344
stanza/utils/conll18_ud_eval.py,sha256=iWpitP9Rvt3h7q5Gsdt4TaEzEr8NHHo8XRzCmTjGy30,27808
stanza/utils/contract_mwt.py,sha256=8XPI_B9ABC_xHSj0nf-W-NrJevmOQnM9pg13HTs1cTg,1301
stanza/utils/helper_func.py,sha256=vixVm0ail9cZXaNGK7beJ3-jvnWj9XMZDiu9RJQWCyI,1263
stanza/utils/max_mwt_length.py,sha256=plVi0jks50TBsEOz8GKnlXxqjsn2uq6KH38VEAGxGsw,246
stanza/utils/postprocess_vietnamese_tokenizer_data.py,sha256=q5q14Uq8SF9INFNRUYRtxBmdYplfIDL6GtsmHGR0jdo,2456
stanza/utils/prepare_ner_data.py,sha256=3ivcnnigg6BE3FfekkOaQhEkaYjDKkgOaMx2_Q7krA0,2225
stanza/utils/prepare_tokenizer_data.py,sha256=Z5ogzPvXTtO_VBSFkEz98fuJILJToUx0YUy-VGtpGE4,5072
stanza/utils/select_backoff.py,sha256=sjx3-Glmmk6UahYDFCQ5TiIvV7u-MdgZXnPt6zLaFFU,451
tests/__init__.py,sha256=JRmO_ghptNnbvW-nCXT1-Yjq76xMGsSRn0FJSquItr0,4411
tests/__pycache__/__init__.cpython-310.pyc,,
tests/__pycache__/test_client.cpython-310.pyc,,
tests/__pycache__/test_data_conversion.cpython-310.pyc,,
tests/__pycache__/test_data_objects.cpython-310.pyc,,
tests/__pycache__/test_decorators.cpython-310.pyc,,
tests/__pycache__/test_depparse.cpython-310.pyc,,
tests/__pycache__/test_depparse_data.cpython-310.pyc,,
tests/__pycache__/test_doc.cpython-310.pyc,,
tests/__pycache__/test_english_pipeline.cpython-310.pyc,,
tests/__pycache__/test_installation.cpython-310.pyc,,
tests/__pycache__/test_lemmatizer.cpython-310.pyc,,
tests/__pycache__/test_mwt_expander.cpython-310.pyc,,
tests/__pycache__/test_ner_tagger.cpython-310.pyc,,
tests/__pycache__/test_pretrain.cpython-310.pyc,,
tests/__pycache__/test_protobuf.cpython-310.pyc,,
tests/__pycache__/test_requirements.cpython-310.pyc,,
tests/__pycache__/test_semgrex.cpython-310.pyc,,
tests/__pycache__/test_server_misc.cpython-310.pyc,,
tests/__pycache__/test_server_request.cpython-310.pyc,,
tests/__pycache__/test_server_start.cpython-310.pyc,,
tests/__pycache__/test_tagger.cpython-310.pyc,,
tests/__pycache__/test_tokenizer.cpython-310.pyc,,
tests/__pycache__/test_utils.cpython-310.pyc,,
tests/test_client.py,sha256=dn8xlXgehSq3CCahj8ESWGxzVhi9RnNECANhyl1uQTE,8155
tests/test_data_conversion.py,sha256=ML-gzToElOsilsfi85WbU4hOmUtOZ_GubcrUVe8Bn-4,3410
tests/test_data_objects.py,sha256=b8qHVZdU-0jnizAChd-7KbQMg1si1NnnyM2Cac1SpcQ,1580
tests/test_decorators.py,sha256=dTbevp2dMW9DffYI8VwySWwySQTdRx86HT1P1QyiEWk,4877
tests/test_depparse.py,sha256=XAbCzBRancngiQ0R01peJPJFTmEnUxy_mJhLMImKgo0,2552
tests/test_depparse_data.py,sha256=gswInkD5Q5Futirsgs9s8yWvfS-HAIAtQ84kvS3YzqA,2466
tests/test_doc.py,sha256=mpCmdW8ZJeeOIDOrilmLm6fRFffuL0RgZeoICMSe07I,1541
tests/test_english_pipeline.py,sha256=5phtXI50cA9gLKDGVmBXES1NhLRJiHBf7f8rECDn9NI,6937
tests/test_installation.py,sha256=N3ZcQdxxUwgnkymR6CmTdHu1sS3JI-zTDV9hqiar0kE,1421
tests/test_lemmatizer.py,sha256=F6Mq2L9kqryKERjFKkvD005XJBfFMh-Ji4ireFySJZU,1123
tests/test_mwt_expander.py,sha256=376EfB4svol6nugMufyEBMwdcT6GwUNtdWhldMpcjrQ,3356
tests/test_ner_tagger.py,sha256=BX5MSR1IPd_A7IZrJLBZzUtPS3PtjkwHTDaohDYvTiw,1511
tests/test_pretrain.py,sha256=NZCd683Vi-eGMqo_VZzcDOh_HFWGNLLaKxEaGmEYJ28,1849
tests/test_protobuf.py,sha256=5b2V-gxtrlTfnwqq5ieFMFK2dKNsHwQU8sRRjKgDxI0,4605
tests/test_requirements.py,sha256=yMXq-bYkH9yhAoYdHh6ARAVYOAUhTGYsJKqMxwBuIDE,3086
tests/test_semgrex.py,sha256=uABSZ2RQTp3crquDfvoVhPCi604EYWZ6BOPCJlehvGE,6577
tests/test_server_misc.py,sha256=1SztYBJtxvGpGuhsnWiogp1fDh9Oh38bZ4nsB9zlS_s,4239
tests/test_server_request.py,sha256=-hKhgtnMHZkZ5w1Gn8jtBFrWsYWqhp2D7AIXW1U6khc,10264
tests/test_server_start.py,sha256=LlbhkVuXI1N8p3Ei3-CD9kIWWydJlMmlc8xCV1TCcXI,9725
tests/test_tagger.py,sha256=QK4ln5tlTI_0KOL1VQvBtwzsW4LUIKUVIA6Xxp6ym6o,1036
tests/test_tokenizer.py,sha256=FnmntsSpmmXsNSuCDjkqRR8N3mZ9e-ArHEG5PpZ9_Ig,8043
tests/test_utils.py,sha256=GRIuwlqAv3zAMYNArwchgikhPD8CV05KE7eG9Aus3t4,2755
